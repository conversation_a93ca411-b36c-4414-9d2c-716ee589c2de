import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ArrowUpRight, Instagram, Linkedin, Mail, MapPin, Phone } from "lucide-react";
import React from "react";
import PhoneInput from "./components/PhoneInput";
import Link from "next/link";

const page = () => {
  return (
    <div className=" mx-auto mt-30 ">
      <div className="grid grid-cols-1 lg:grid-cols-2  gap-8">
        {/* Contact Form */}
        <div className="">
          <div className="bg-white rounded-lg  p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              تواصل معنا
            </h1>
            <p className="text-gray-600 mb-8">
              مثال على وصف الخدمة لدينا فقط أريد بناء موقعك، نطلب ذلك تعبئة
              عملية البناء والطريق في مكتب التوثيق أو خدمات كاتب العدل لتسجيل
              العقار بسلاسة.
            </p>

            <form className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الاسم *
                </label>
                <Input
                  placeholder="ادخل الاسم"
                  className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                />
              </div>

              <div className="flex gap-4 w-full">
                <div className="w-full">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الالكتروني *
                  </label>
                  <Input
                    type="email"
                    placeholder="ادخل البريد الالكتروني"
                    className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                  />
                </div>
                <div className="w-full">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الجوال *
                  </label>
                  <div className="flex">
                    <PhoneInput />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  موضوع الاستفسار{" "}
                </label>
                <Input
                  placeholder="ادخل موضوع الاستفسار"
                  className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كيف يمكننا المساعدة؟
                </label>
                <Textarea
                  placeholder="يمكنك كتابة ما تريد مساعدتك به هنا..."
                  rows={4}
                  className="w-full min-h-[125px] shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                />
              </div>

              <div className="w-full flex justify-end">
                <Button
                  size="lg"
                  className="bg-[#5840BA] text-[#FFF]  rounded-3xl px-[8px] md:px-[16px] py-[8px] md:py-[8px] text-sm md:text-[15px] font-semibold"
                >
                  إرسال
                  <ArrowUpRight className="w-6 h-6 md:w-7 md:h-7 ml-2" />
                </Button>
              </div>
            </form>
          </div>
        </div>
        {/* Contact Information Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg  border border-[#D2D6DB] max-w-xl p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              بيانات الاتصال
            </h2>

            <div className="space-y-6">
              <div className="flex items-start">
                <Phone className="w-5 h-5 text-purple-600 mt-1 ml-3" />
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">رقم الجوال</h3>
                  <p className="text-gray-600">+966 55 150 7434</p>
                </div>
              </div>

              <div className="flex items-start">
                <Mail className="w-5 h-5 text-purple-600 mt-1 ml-3" />
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">
                    البريد الالكتروني
                  </h3>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start">
                <MapPin className="w-5 h-5 text-purple-600 mt-1 ml-3" />
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">الموقع</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    شرق رقم 7266، طريق الملك فهد، حي الملقا، 13331 الرياض
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <p className="text-sm text-gray-600 mb-4">
                تابع آخر أخبارنا على شبكات التواصل
              </p>
              <div className="flex gap-3">
                {/* Instagram */}
                <Link
                  href="#"
                  className="w-8 h-8 border-1 border-[#ECECECE5] bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
                >
                  <Instagram />
                </Link>
                {/* X (Twitter) */}
                <Link
                  href="#"
                  className="w-8 h-8 border-1 border-[#ECECECE5]  bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
                >
                  <svg
                    className="w-6 h-6 text-black text-sm font-[400]"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                </Link>

                {/* LinkedIn */}
                <Link
                  href="#"
                  className="w-8 h-8 border-1 border-[#ECECECE5] bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
                >
                  <Linkedin />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
